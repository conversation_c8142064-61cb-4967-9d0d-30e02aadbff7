乐哥，资料已足够。结合你上传的源码（getUrl.php / common.php / getNews.php 等）与官方图文手册，我确认插件对 JSON 接口是支持的：type=2 分支会按“开始/结束标记→json_decode→扁平化（changeArr）→is_url 过滤→must/not_include 过滤”的流程工作，而只有在传入是字符串时才会用 <a href> 正则。你遇到“未采集到url”的主因是截取区间与字段、以及绝对 URL 校验不匹配，而不是系统把 JSON 当 HTML。

下面给你两套可直接落地的方案：

⸻

方案A（推荐，最省心）——用 RSSHub 输出 JSON Feed

为什么选它
头条接口存在签名/反爬与字段多样性问题；RSSHub 已封装了路由并支持 ?format=json，返回标准 JSON Feed，字段稳定（items[].title/url/date_published/content_html）。你只需一次设置即可长稳运行。 ￼ ￼

列表页设置（采集接口 / type=2）
	•	列表页URL匹配规则

https://rsshub.app/jinritoutiao/keyword/娱乐?format=json

如需更精准，可再建节点：
…/keyword/明星?format=json、…/keyword/好莱坞?format=json。 ￼

	•	开始标记："items":[
	•	结束标记：优先 ]}；若测试失败，改 ]," 或 ],"title"（以能成功匹配整段 items 并解出 JSON 为准）。
	•	URL中必须包含：https://www.toutiao.com/
	•	URL中必须不包含："video","live","image"（可先留空，跑通后再加）。

字段映射（在同一 JSON 片段内提取）

字段	开始标记	结束标记	备注
标题	"title":"	",	必填
详情URL	"url":"	",	绝对 URL，可直接过 is_url
发布时间	"date_published":"	",	ISO 时间
文章来源	"author":{"name":"	"	若无可留空
正文HTML	"content_html":"	",	已是全文 HTML（有转义）
摘要	"summary":"	",	可选
封面	"image":"	",	若为空可从 content_html 取第一图

说明：RSSHub JSON Feed 字段设计见其通用文档；?format=json 返回 JSON Feed 规范，顶层含 items。

验证要点
	1.	测试匹配应返回 10~30 条 https://www.toutiao.com/ 开头链接。
	2.	若提示“未采集到url”：把结束标记改为 ]} 再测；或临时清空“必须包含”观察是否能出 URL。
	3.	若正文为空，先用 content_html；必要时再启用方案B抓详情页。

⸻

方案B（直连头条 JSON 接口）

优点：少一跳依赖。
风险：字段、签名参数及风控可能变化；需按返回结构微调。
头条搜索/频道接口返回通常包含 data:[ {...} ]，单项中常见：article_url、title、abstract、media_name、behot_time（秒级时间戳）、image_list[].url 等；示例中 article_url 为 http://toutiao.com/group/…。 ￼ ￼

建议使用的列表 API（示例）
	•	关键词综合搜索（无需登录，较稳定）：

https://www.toutiao.com/api/search/content/?offset=(*)&format=json&keyword=%E5%A8%B1%E4%B9%90&cur_tab=1&from=search_tab&pd=synthesis

(*) 用作页码占位，offset 以 0、20、40… 递增。字段里通常含 article_url。该端点存在风控与参数演进，若空返回，优先切回方案A。该字段示例与结构参考开源项目展示的样例。 ￼ ￼

列表页设置（采集接口 / type=2）
	•	列表页URL匹配规则

https://www.toutiao.com/api/search/content/?offset=(*)&format=json&keyword=%E5%A8%B1%E4%B9%90&cur_tab=1&from=search_tab&pd=synthesis


	•	开始标记："data":[
	•	结束标记：],"has_more"（不行则试 ],"message"、或干脆 ]}）。
	•	URL中必须包含：toutiao.com（先宽，确保不过滤掉 http://toutiao.com/group/... 与 https://www.toutiao.com/...）。
	•	URL中必须不包含：先留空；跑通后可添加 image,video,live 等。

关键点：is_url() 只接受带协议的绝对 URL；若接口返回 //www.toutiao.com/... 或 /group/... 将被丢弃。优先使用含 http 的字段（article_url / display_url / share_url）。 ￼

字段映射（从同一 JSON 片段中取）

目标字段	开始标记	结束标记	备选键名
标题	"title":"	",	仅含纯文本
详情URL	"article_url":"	",	备选："share_url":"、"display_url":"
摘要	"abstract":"	",	无则留空
发布时间	"behot_time":	,	也可用 "create_time":
来源/作者	"media_name":"	",	备选："source":"
封面	"image_list":[{"url":"	"	如无 image_list，改抓 "middle_image":{"url":"

若仍无绝对 URL：临时把“必须包含”置空，让系统先入库 URL，再在导出或发布前做补全；但更建议切回有绝对 URL 的键名。字段示例见开源样例。 ￼

详情页正文（必须配置）

头条列表 JSON 不保证提供全文，需进入详情页抓取。不同文章模版不一，通用锚点可先试以下顺序（任选其一命中即可）：
	1.	正文开始：<div class="article-content" 正文结束：</div>
	2.	正文开始：<article 正文结束：</article>
	3.	正文开始：<div class="tt-article-content" 正文结束：</div>
	4.	若为 M 站：<div class="article-content" / </div>

这些锚点是基于头条 Web 详情页在 WebView 中承载并使用语义化标签/容器的常见做法与行业经验；不同来源内容可能落入不同容器。若正文为空，可在“查看源码”里搜索“article、article-content、tt-article”等类名再微调。对于多源混抓，像八爪鱼那样分支适配更稳。 ￼ ￼ ￼

⸻

核对与排错清单
	1.	先看 JSON 是否被成功解码
	•	“测试匹配”后仍报错，多半是 结束标记过早/过晚。把结束标记改为 ]} 再试。
	•	也可把开始标记改成更靠近数组起点的 "data":[ / "items":[。
	2.	确认拿到的是绝对 URL
	•	在“获取到的种子网址”应看到 http…toutiao.com/…。若为空，检查是否抓到 // 或 /group/ 开头的链接被 is_url() 丢弃。
	3.	must_include 不要太严
	•	先用 toutiao.com。等跑通后再收紧到 /group/。
	4.	正文为空
	•	切到另一套正文锚点；或直接使用方案A的 content_html。
	5.	风控/空返回
	•	将采集间隔拉到 ≥3 秒，单次数量 ≤10；若仍不稳，切回方案A。头条 Web 端接口签名与风控常有变化，社区项目也提醒了这一点。 ￼ ￼

⸻

建议的运行参数（先稳再快）
	•	采集频率：每 30 分钟（娱乐更新快，且避风控）。
	•	每次条数：10 条。
	•	间隔：3 秒。
	•	去重：以 url 或 group_id（若可取到）作为唯一键。

⸻

7天低成本验证计划
	1.	稳定性：记录 7 天采集任务成功率（目标 ≥95%）；若低于 90%，统一切换到方案A。
	2.	内容质量：抽检 50 篇，正文完整率 ≥90%，图片首图命中率 ≥85%。
	3.	效果：对“娱乐”“明星”“影视”三组关键词各推 30 篇，统计点击、停留、完读，淘汰噪音高的关键词。
	4.	白名单补强（可选）：对头条优质作者用 RSSHub 的 token 路由建独立节点，形成“关键词发现 + 作者沉淀”的双轨。 ￼

⸻

是否还需要更多信息？

现有源码与文档足够落地。如果要我一次到位配置，只需你补充两点：
	1.	你更偏好 方案A（稳定） 还是 方案B（直连）？
	2.	若走方案A，是否要分三条关键词路由（娱乐 / 明星 / 影视）分别入库？

⸻

快速粘贴清单（两套方案，复制即用）

A. RSSHub（推荐）
	•	列表规则：
https://rsshub.app/jinritoutiao/keyword/娱乐?format=json
	•	开始标记："items":[
	•	结束标记：]}（不行改 ]," / ],"title"）
	•	必须包含：https://www.toutiao.com/
	•	不包含：video,live,image
	•	标题："title":" … ",
	•	URL："url":" … ",
	•	时间："date_published":" … ",
	•	正文："content_html":" … ",
	•	来源："author":{"name":" … "

B. 直连头条
	•	列表规则：
https://www.toutiao.com/api/search/content/?offset=(*)&format=json&keyword=%E5%A8%B1%E4%B9%90&cur_tab=1&from=search_tab&pd=synthesis
	•	开始标记："data":[
	•	结束标记：],"has_more"（不行改 ]}）
	•	必须包含：toutiao.com
	•	不包含：（先留空）
	•	URL："article_url":" … ",（备选："share_url":" / "display_url":"）
	•	标题："title":" … ",
	•	摘要："abstract":" … ",
	•	时间："behot_time": … ,
	•	来源/作者："media_name":" … ",
	•	封面："image_list":[{"url":" … "
	•	详情页正文（任选其一命中）：
	•	<div class="article-content" … </div>
	•	<article … </article>
	•	<div class="tt-article-content" … </div>

⸻

引用
	•	头条 Web 搜索接口返回示例字段（article_url、behot_time、media_name 等）。示例 JSON 片段展示。 ￼
	•	社区对头条 API 的梳理与第三方实现，提示签名/风控与字段差异。 ￼ ￼
	•	RSSHub 支持 ?format=json（JSON Feed）及头条相关路由，可直接获取 items[].url/content_html。
	•	多源详情页结构差异与分支适配的实践参考。 ￼
	•	头条详情页通过 WebView 承载、语义化容器的行业背景。 ￼ ￼

⸻

复述你的输入

你提供了火鸟采集插件的源码与官方文档，并要求我据此判断资料是否足够；若足够，直接给出一套能解决“今日头条娱乐”采集报错并稳定运行的最终方案。

为你优化后的 Prompt（可投给任意 Agent 执行）

任务：在 hawaiihub.net 的火鸟“信息资讯采集插件”中，搭建 “今日头条·娱乐”自动采集，要求稳定、低维护。
约束：插件 type=2（JSON）流程：开始/结束标记→json_decode→changeArr→is_url（仅绝对 URL）→must/not_include 过滤。
输出：
	1.	提供 方案A（RSSHub） 与 方案B（直连头条） 两套配置，各给出可直接粘贴的：列表URL、开始/结束标记、必须包含/不包含、字段映射（标题、URL、时间、来源、封面、正文/或 content_html），以及 3 个可替换的结束标记备选。
	2.	给出 详情页正文 的 3 组通用锚点，并说明当正文为空时的回退策略。
	3.	制定 排错清单（json 解码失败、相对 URL 被 is_url 丢弃、must_include 过严等）与 7 天验证指标（成功率≥95%、正文完整率≥90%）。
	4.	给出 运行参数（每 30 分钟、每次 10 条、间隔 3 秒）与 去重键（url/group_id）。
	5.	提供基于 RSSHub 文档与开源项目的简要引用。

要我现在就按方案A把三个关键词节点（娱乐/明星/影视）一起配置出来吗？我可以按上述清单把每个字段的填写值逐项发给你。
 
---

## 最终执行步骤（一步到位）

**目标**：在火鸟“信息资讯采集插件”里稳定跑通“今日头条·娱乐”。默认采用 **方案A：RSSHub JSON Feed**，直连头条作为备用。

### Step 1｜创建 3 个 RSSHub 节点（推荐）
为降低噪音与提升覆盖，分别用 3 个关键词：**娱乐 / 明星 / 影视**。

> 节点 1：今日头条·娱乐（RSSHub）
- 列表页URL匹配规则：
  ```
  https://rsshub.app/jinritoutiao/keyword/娱乐?format=json
  ```
- 开始标记：`"items":[`
- 结束标记（按顺序尝试，命中即止）：
  1. `]}`
  2. `],"`
  3. `],"title"`
- URL中必须包含：`https://www.toutiao.com/`
- URL中必须不包含：`video,live,image`（若影响命中，可暂留空）
- 字段映射：
  - 标题：`"title":"`  … `",`
  - 链接：`"url":"`  … `",`
  - 发布时间：`"date_published":"`  … `",`
  - 正文HTML：`"content_html":"`  … `",`
  - 来源：`"author":{"name":"`  … `"`
  - 摘要（可选）：`"summary":"`  … `",`
  - 封面（可选）：`"image":"`  … `",`

> 节点 2：今日头条·明星（RSSHub）
- 列表：`https://rsshub.app/jinritoutiao/keyword/明星?format=json`
- 其余配置 **与节点 1 相同**。

> 节点 3：今日头条·影视（RSSHub）
- 列表：`https://rsshub.app/jinritoutiao/keyword/影视?format=json`
- 其余配置 **与节点 1 相同**。

### Step 2｜测试与发布
1. 在“采集”页点击 **测试匹配**，应出现 ≥10 条 `https://www.toutiao.com/` 链接。
2. 点击 **开始采集**，观察“获取到的种子网址”是否填充；若空：
   - 将 **结束标记** 改为 `]}` 再试；
   - 临时清空“必须包含”，验证是否被过滤；
   - 若 `url` 为相对地址（不含协议），换抓 `url` 字段为绝对链接的节点或切回 RSSHub。
3. 字段抽检：随机开 5 篇，确认标题、时间、正文、首图正常。
4. 发布：先导入待审库，抽检后再开启自动发布。

### Step 3｜运行参数（初始）
- 定时：每 **30 分钟**；
- 每次：**10 条**；
- 间隔：**3 秒**；
- 去重键：`url`（或可用 `group_id` 时以其为主）。

---

## 备用方案（直连头条 JSON）

> 当 RSSHub 临时不可用或需要更细粒度控制时使用。

- 列表页URL（示例，offset=0/20/40…）：
  ```
  https://www.toutiao.com/api/search/content/?offset=(*)&format=json&keyword=%E5%A8%B1%E4%B9%90&cur_tab=1&from=search_tab&pd=synthesis
  ```
- 开始标记：`"data":[`
- 结束标记（依次尝试）：
  1. `],"has_more"`
  2. `],"message"`
  3. `]}`
- 必须包含：`toutiao.com`（先宽松，跑通后可收紧到 `/group/`）
- 字段映射优先级：
  - 链接：`"article_url":"`  … `",`  （备选 `"share_url":"` / `"display_url":"`）
  - 标题：`"title":"`  … `",`
  - 摘要：`"abstract":"`  … `",`
  - 时间：`"behot_time":`  … `,`（秒）
  - 来源：`"media_name":"`  … `",`
  - 封面：`"image_list":[{"url":"` … `"`

> **正文抓取（必设）**：不同模板任选命中一组
- `<div class="article-content"` … `</div>`
- `<article` … `</article>`
- `<div class="tt-article-content"` … `</div>`

---

## 排错速查
- **未采集到url**：结束标记不对 → 改为 `]}`；或“必须包含”过严；或取到相对链接被 `is_url()` 丢弃。
- **正文为空**：换用另一组正文锚点；或直接使用 RSSHub 的 `content_html`。
- **返回为空/被风控**：把间隔 ≥3 秒，单次 ≤10；必要时切回 RSSHub。

---

## 7 天验证指标
- 任务成功率 ≥ **95%**；
- 正文完整率 ≥ **90%**；
- 首图命中率 ≥ **85%**；
- 噪音来源（不相关稿件）≤ **10%**。达不到即调整关键词或切换白名单作者路由。

---

## 变更记录
- **2025-07-27**：首次定稿。新增“三节点 RSSHub 方案 + 直连头条备用 + 排错清单 + 指标”。