# 今日头条娱乐新闻采集技术实施报告

**项目负责人**：乐哥  
**报告日期**：2025年7月28日  
**技术分析师**：Augment Agent  

## 一、问题诊断总结

### 1.1 核心问题分析（修正）
经过对火鸟采集插件源码的再次核对，**系统并不是把 JSON 当 HTML 处理**。当节点类型为 **type=2（采集接口 / JSON）** 时，流程是：

1) 先按“开始标记 / 结束标记”截取文本片段；
2) 尝试 `json_decode($body, true)` 将其解析为数组；
3) 若失败，再用 `json_reg()` 抽取 JSON，再次解析；
4) 解析成功后，通过 `changeJsonToArr()` / `changeArr()` **递归扁平化**；
5) 然后在 `getUrls()` 中对**数组**做遍历，提取候选链接；
6) 仅当传入的是**字符串**时，才会使用 `<a href>` 的 HTML 正则。
7) 最终用 `is_url()` 只保留**带协议的绝对 URL**，再经过 `must_include / not_include` 过滤。

> 因此，出现“未采集到 url”的真正原因通常是：
- **裁剪区间不正确**，导致 `json_decode` 失败或截到的片段里没有目标字段；
- 从 JSON 中提取到的链接是 **相对地址或协议相对地址**（如 `/group/...`、`//www.toutiao.com/...`），被 `is_url()` 丢弃；
- **必须包含** 规则过严，过滤掉了正确的 `share_url / display_url / article_url`；
- 字段名与返回结构不一致（不同接口可能用 `url`、`share_url`、`display_url`、`article_url`）。

### 1.2 系统架构分析
火鸟采集插件支持三种采集类型：
- **type=1**：单页面采集（HTML）
- **type=2**：采集接口（JSON），含 `json_decode` + 扁平化 + 绝对 URL 校验
- **type=3**：多页面采集（循环翻页）

**关键发现（更正）**：系统对 **JSON** 已有完备支持；问题主要在于 **开始/结束标记截取不准** 与 **绝对 URL 过滤** 配置不当，而不是架构缺陷。

## 二、解决方案对比分析

### 2.1 GPT-o3方案分析

**方案A优势**（RSSHub方案）：
- ✅ 技术架构稳定，使用标准JSON Feed格式
- ✅ 字段结构统一，减少维护成本
- ✅ 避免今日头条反爬机制
- ✅ 提供完整的 `content_html` 字段
- ✅ 支持通过 \`?format=json\` 直接输出 **JSON Feed**（items[].title/url/content_html/date_published），字段稳定。

**方案B特点**（直连头条）：
- ✅ 减少中间依赖
- ⚠️ 存在反爬风险
- ⚠️ API参数可能变化

### 2.2 技术可行性验证

基于源码分析，两个方案都技术可行，但需要注意：

**URL提取关键点**：
```php
// common.php 第210-216行：URL验证函数
function is_url($url){
    if(filter_var($url,FILTER_VALIDATE_URL)){
        return true;
    }else{
        return false;
    }
}
```

**必须确保提取的URL为绝对路径格式**。

## 三、推荐实施方案

### 3.1 优选方案：RSSHub + 直连头条混合策略

**主要采集源**：RSSHub（稳定性优先）
**备用采集源**：直连头条API（覆盖度补充）

### 3.1.1 部署选型

**模式0｜直接使用公共 RSSHub（首选起步）**  
在插件里直接填 `https://rsshub.app/...?...&format=json`，零部署、立即可用。公共实例偶有限流，可通过“实例切换表”兜底。〔参考：RSSHub 文档指出可用 `format=json` 输出 JSON Feed，并支持将 `rsshub.app` 替换为任意公共或自建实例。〕

**模式1｜自建 RSSHub（Node.js + pm2）**  
当需要稳定 SLA 时，在你的服务器上启动 RSSHub：`pnpm i && pnpm build && PORT=1200 pnpm start`，或 `pm2 start lib/index.js --name rsshub -- --port 1200`，再用 Nginx 反代。仅将插件中的域名从 `rsshub.app` 改为你的域名即可。 

**模式2｜多公共实例切换**  
准备 2–3 个公共实例作为备选，出现 429/超时或 7 天成功率 <90% 即切换。

### 3.1.2 公共实例切换表

| 优先级 | 实例域名 | 说明 | 健康度参考 |
|---|---|---|---|
| 1 | `https://rsshub.app` | 官方演示实例，最常用 | 官方推荐，可替换为自建/公共实例 |
| 2 | `https://rss.owo.nz` | 社区高并发实例，提供健康面板 | 示例健康页显示持续运行与请求频度。 |
| 3 | `https://rsshub.pseudoyu.com` | 社区维护实例 | 健康与命中率信息可查 |
| 4 | `https://rsshub.chn.moe` | 社区实例 | 作为备用 |

> 说明：以上为可用示例，公共实例可随时变化。若无法访问，请切换或改用自建。

### 3.2 具体配置参数

#### 方案A：RSSHub配置（推荐）

**节点1：今日头条·娱乐（RSSHub）**
```
列表页URL：https://rsshub.app/jinritoutiao/keyword/娱乐?format=json
采集类型：采集接口（type=2）
开始标记："items":[
结束标记：]}
必须包含：https://www.toutiao.com/
必须不包含：video,live,image

字段映射：
- 标题开始："title":"    结束：",
- 链接开始："url":"     结束：",
- 时间开始："date_published":"  结束：",
- 正文开始："content_html":"   结束：",
- 来源开始："author":{"name":"  结束："
```

**节点2：明星**
- 列表页URL：https://rsshub.app/jinritoutiao/keyword/明星?format=json
- 其余配置同节点1。

**节点3：影视**
- 列表页URL：https://rsshub.app/jinritoutiao/keyword/影视?format=json
- 其余配置同节点1。

#### 方案B：直连头条配置（备用）

**节点2：今日头条·娱乐（直连）**
```
列表页URL：https://www.toutiao.com/api/search/content/?offset=(*)&format=json&keyword=%E5%A8%B1%E4%B9%90&cur_tab=1&from=search_tab&pd=synthesis
采集类型：采集接口（type=2）
开始标记："data":[
结束标记：],"has_more"
必须包含：toutiao.com
必须不包含：（暂时留空）

字段映射：
- 标题开始："title":"      结束：",
- 链接开始："article_url":"  结束：",
- 摘要开始："abstract":"    结束：",
- 时间开始："behot_time":   结束：,
- 来源开始："media_name":"  结束：",

详情页正文提取（必须配置）：
- 开始标记：<div class="article-content"
- 结束标记：</div>
```

## 四、分步实施计划

### 4.1 第一阶段：RSSHub方案部署（1-2天）

**步骤1：创建采集节点**
1. 登录HawaiiHub后台 → 信息资讯采集插件
2. 点击"添加节点"，按照方案A配置参数
3. 保存并测试URL匹配功能

**步骤2：字段映射配置**
1. 配置标题、链接、时间、正文等字段提取规则
2. 测试字段提取效果
3. 调整开始/结束标记确保准确性

**步骤3：采集测试**
1. 设置小批量测试（每次5条，间隔3秒）
2. 验证采集内容质量
3. 检查去重和错误处理

**步骤4：实例切换演练**
- 将列表 URL 的域名从 `rsshub.app` 改为 `rss.owo.nz`，再次“测试匹配”。
- 记录两次匹配的响应时间与命中条数，作为切换基线。

### 4.2 第二阶段：备用方案配置（2-3天）

**步骤4：直连头条节点**
1. 创建备用采集节点
2. 配置详情页正文提取规则
3. 测试反爬机制应对

**步骤5：监控系统建立**
1. 配置采集成功率监控
2. 设置异常告警机制
3. 建立日志分析系统

### 4.3 第三阶段：优化与扩展（3-5天）

**步骤6：多关键词扩展**
1. 创建"明星"、"影视"关键词节点
2. 优化采集频率和去重策略
3. 建立内容质量评估机制

## 五、风险评估与控制

### 5.1 技术风险

**高风险**：
- 今日头条API参数变化（概率：30%）
- RSSHub服务不稳定（概率：15%）

**中风险**：
- 采集内容质量下降（概率：25%）
- 系统性能影响（概率：20%）

**低风险**：
- 字段映射失效（概率：10%）

### 5.2 风险控制措施

**技术保障**：
- 双重采集源备份
- 自动故障切换机制
- 实时监控告警

**运营保障**：
- 7天验证期监控
- 内容质量人工抽检
- 采集频率动态调整

## 六、预期效果与验证指标

### 6.1 性能指标

**采集效率**：
- URL提取成功率：≥95%
- 内容采集成功率：≥90%
- 字段完整性：≥90%

**系统稳定性**：
- 7天连续运行成功率：≥95%
- 平均响应时间：≤5秒
- 错误重试成功率：≥80%
- **429/超时**：< 3 次/天
## 八、引用与依据

- RSSHub 支持 `format=json`（JSON Feed / Atom / RSS / RSS3）输出，并可将 `rsshub.app` 替换为任意公共或自建实例。文档：参数与入门。〔引用：docs.rsshub.app/guide/parameters；docs.rsshub.app/guide/〕
- 高级文档对 JSON Feed 兼容字段有说明。〔引用：docs.rsshub.app/joinus/advanced/advanced-feed〕
- 公共实例可替代使用，社区有公开实例/健康页示例：`rss.owo.nz`、`rsshub.pseudoyu.com`、`rsshub.chn.moe`。〔引用：上述站点首页健康信息；GitHub Issue #8806 讨论公共实例列表需求〕
- RSSHub 仓库含 JSON 相关实现与转换代码（如 `transform/json.ts`），证明 JSON 输出的一致性。〔引用：github.com/DIYgod/RSSHub/.../transform/json.ts〕

### 6.2 内容质量指标

**内容相关性**：
- 娱乐新闻相关度：≥90%
- 重复内容比例：≤5%
- 标题完整性：≥95%

**用户体验**：
- 图片加载成功率：≥85%
- 正文可读性：≥90%
- 发布时间准确性：≥95%

## 七、运营建议

### 7.1 采集策略

**频率控制**：
- 初期：每30分钟采集一次
- 稳定期：每15分钟采集一次
- 每次采集：10-15条内容

**质量控制**：
- 建立关键词黑名单
- 设置内容长度阈值
- 启用人工审核机制

### 7.2 监控维护

**日常监控**：
- 每日检查采集成功率
- 每周分析内容质量报告
- 每月优化采集规则

**应急预案**：
- 主要采集源故障时自动切换备用源
- 采集成功率低于80%时暂停并告警
- 发现大量重复内容时调整去重策略

## 九、关键技术差异对比

### 9.1 我的方案 vs GPT-o3方案

**相同点**：
- 都识别出火鸟系统支持JSON采集（type=2）
- 都推荐RSSHub作为主要方案
- 都提供了直连头条的备用方案

**关键差异**：

| 技术要点 | 我的分析 | GPT-o3分析 | 准确性评估 |
|---------|---------|-----------|-----------|
| 问题根因 | URL提取正则表达式不匹配JSON | 截取区间与字段不匹配 | GPT-o3更准确 |
| 源码理解 | 认为系统把JSON当HTML处理 | 确认系统支持JSON解析 | GPT-o3更深入 |
| 配置精度 | 提供基础配置参数 | 提供多套备选标记 | GPT-o3更实用 |
| 风险评估 | 重点关注技术风险 | 强调反爬和参数变化 | GPT-o3更全面 |

### 9.2 技术准确性验证

经过源码深度分析，**GPT-o3的技术判断更加准确**：

1. **JSON处理机制**：火鸟系统确实支持JSON解析，问题在于配置不当
2. **URL提取逻辑**：系统会先解析JSON，再从解析结果中提取URL
3. **错误原因**：主要是开始/结束标记设置不精确，导致无法正确截取数据

## 十、最终推荐方案

### 10.1 采用GPT-o3的技术方案

基于源码验证和技术对比，**推荐采用GPT-o3提供的配置方案**：

**优选配置**（RSSHub）：
```
列表页URL：https://rsshub.app/jinritoutiao/keyword/娱乐?format=json
开始标记："items":[
结束标记：]} （备选：]," 或 ],"title"）
必须包含：https://www.toutiao.com/
字段映射完全按照GPT-o3方案执行
```

**备用配置**（直连头条）：
```
完全采用GPT-o3提供的参数配置
特别注意多套结束标记备选方案
重点关注绝对URL的提取
```

### 10.2 实施建议

1. **立即执行GPT-o3方案**：技术更准确，配置更精细
2. **保留我的监控策略**：补充完善的风险控制措施
3. **结合两方优势**：技术配置用GPT-o3，运营管理用我的方案

## 十一、总结与建议

**技术方案选择**：GPT-o3方案技术更准确，配置更可靠
**风险控制策略**：结合两方案的优势，建立完善的监控体系
**实施路径**：优先部署GPT-o3的RSSHub方案，确保快速上线

该综合方案将确保HawaiiHub华人平台娱乐新闻采集功能的稳定运行。

## 附录A｜自建 RSSHub（无 Docker）部署手册

> 目标：在同一台服务器（宝塔/BT 或裸机）上用 **Node.js + pnpm + pm2** 启动 RSSHub，并通过 Nginx 反向代理为外网提供 `https://rss.hawaiihub.net` 服务。

### A.1 环境准备
- Node.js ≥ 18（建议 20 LTS）
- pnpm ≥ 9
- pm2 ≥ 5

**macOS（Apple Silicon）/ Linux 安装示例**
```bash
# Node（任选其一方式）
brew install node            # macOS 推荐
# 或使用 nvm：
# curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
# nvm install 20 && nvm use 20

# 包管理器与进程守护
npm i -g pnpm pm2
```

### A.2 获取与启动 RSSHub
```bash
# 1) 拉仓库
mkdir -p /opt && cd /opt
git clone https://github.com/DIYgod/RSSHub.git
cd RSSHub

# 2) 安装依赖 & 构建
pnpm i
pnpm build

# 3) 启动（内存缓存即可满足初期需求）
PORT=1200 CACHE_TYPE=memory pnpm start
# 访问测试：curl -I http://127.0.0.1:1200/healthz
```

**使用 pm2 常驻**
```bash
pm2 start lib/index.js --name rsshub -- \
  --port 1200
pm2 save
pm2 status
```

### A.3 Nginx 反向代理（示例）
```nginx
server {
    listen 80;
    server_name rss.hawaiihub.net;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name rss.hawaiihub.net;

    # SSL 证书（Let’s Encrypt 或其他）
    ssl_certificate     /etc/letsencrypt/live/rss.hawaiihub.net/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/rss.hawaiihub.net/privkey.pem;

    gzip on;
    gzip_types application/json application/javascript text/plain text/xml application/xml;

    location / {
        proxy_pass http://127.0.0.1:1200;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_read_timeout 60s;
        proxy_connect_timeout 10s;
    }
}
```
> 配置完成后，浏览器访问 `https://rss.hawaiihub.net/healthz` 应返回 `ok`。

### A.4 常用维护命令
```bash
# 查看/重启
pm2 logs rsshub --lines 200
pm2 restart rsshub
pm2 save

# 升级（在 /opt/RSSHub 下）
pnpm i
pnpm build
pm2 restart rsshub
```

---

## 附录B｜实例健康检查与自动切换（n8n/cron）

### B.1 目标
- 每 10 分钟检测一次主实例 `rsshub.app` 的可用性；
- 若连续失败 ≥ 3 次，自动切换到下一实例 `rss.owo.nz`；
- 同时发送通知（邮件/Telegram/企业微信）。

### B.2 简易 Shell 版（cron）
```bash
#!/usr/bin/env bash
# /usr/local/bin/rsshub-check.sh
set -euo pipefail
PRIMARY="https://rsshub.app/jinritoutiao/keyword/%E5%A8%B1%E4%B9%90?format=json"
FALLBACK="https://rss.owo.nz/jinritoutiao/keyword/%E5%A8%B1%E4%B9%90?format=json"
TARGET_FILE="/opt/rsshub_active_endpoint.txt"
ACTIVE=$(cat "$TARGET_FILE" 2>/dev/null || echo "$PRIMARY")

check() {
  url="$1"
  items=$(curl -m 15 -sS "$url" | jq '.items | length' 2>/dev/null || echo 0)
  if [ "$items" -gt 0 ]; then
    echo "$url" > "$TARGET_FILE"
    echo "OK $items $url"
    exit 0
  else
    return 1
  fi
}

if ! check "$ACTIVE"; then
  # 尝试主实例
  if ! check "$PRIMARY"; then
    # 切到备实例
    check "$FALLBACK" || { echo "ALL_FAILED"; exit 1; }
  fi
fi
```
**crontab**（每 10 分钟）
```cron
*/10 * * * * /usr/local/bin/rsshub-check.sh >> /var/log/rsshub-check.log 2>&1
```
> 火鸟插件里“列表页URL”可引用一个你维护的 **中间配置地址**（例如在 Nginx 上暴露 `/rsshub-active.txt`），由脚本更新其指向，插件无需改动。

### B.3 n8n 工作流思路
1. **HTTP Request**：拉取主实例 JSON。
2. **IF**：判断 `items.length > 0`。
3. **True**：写入“当前实例 = 主实例”；**False**：请求备实例再判断。
4. **Write Binary File / Webhook**：把“当前实例 URL”写到你服务器的小文件或通过 Webhook 告诉后台。
5. **通知**：邮件/Telegram/企业微信推送切换日志（含错误码、响应时间、当前实例）。

---

## 附录C｜火鸟插件快速粘贴模板

> 以 **RSSHub /娱乐** 节点为例。其他关键词节点（明星/影视）只改列表 URL。

```
【节点名称】今日头条·娱乐（RSSHub）
【采集类型】采集接口（type=2）
【列表页URL匹配规则】https://rsshub.app/jinritoutiao/keyword/娱乐?format=json
【开始标记】"items":[
【结束标记】]}
【URL必须包含】https://www.toutiao.com/
【URL必须不包含】video,live,image

【标题—开始】"title":"
【标题—结束】",
【链接—开始】"url":"
【链接—结束】",
【时间—开始】"date_published":"
【时间—结束】",
【正文—开始】"content_html":"
【正文—结束】",
【来源—开始】"author":{"name":"
【来源—结束】"
```

> 如果“未采集到 url”，按顺序尝试把 **结束标记** 改为：`],"` → `],"title"` → `]}`；若仍失败，清空“必须包含”再测，观察是否是过滤过严导致。
----

**技术支持**：建议优先采用GPT-o3的技术配置，如需实施协助请联系技术团队。
 